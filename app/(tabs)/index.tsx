import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Icon } from "@/components/ui/icon";
import { ScrollView } from "@/components/ui/scroll-view";
import { Text } from "@/components/ui/text";
import { View } from "@/components/ui/view";
import { useThemeColor } from "@/hooks/useThemeColor";
import { useBottomTabBarHeight } from "@react-navigation/bottom-tabs";
import {
  Bell,
  Calendar,
  CreditCard,
  DollarSign,
  IdCard,
  Plus,
  QrCode,
  RefreshCw,
  TriangleAlert,
  User,
  Users,
  Wifi,
  WifiOff,
} from "lucide-react-native";
import React, { useMemo } from "react";
import {
  StyleSheet,
  TouchableOpacity,
  useWindowDimensions,
} from "react-native";
import { Separator } from "@/components/ui/separator";

export default function DashboardScreen() {
  const bottom = useBottomTabBarHeight();
  const { width } = useWindowDimensions();

  const primaryColor = useThemeColor({}, "primary");
  const mutedColor = useThemeColor({}, "textMuted");
  const greenColor = useThemeColor({}, "green");
  const redColor = useThemeColor({}, "red");
  const muted = useThemeColor({}, "muted");

  const isOnline: boolean = false;

  // Responsive Quick Actions layout
  const itemWidth = useMemo(() => {
    let cols = 2;
    if (width >= 900) cols = 4;
    else if (width >= 600) cols = 3;
    // Calculate width as a number (accounting for gaps and padding)
    return (width - 40 - (cols - 1) * 12) / cols; // 40px horizontal padding, 12px gaps
  }, [width]);

  const quickActions = [
    { icon: Plus, label: "New Transaction" },
    { icon: QrCode, label: "Scan QR / Search" },
    { icon: IdCard, label: "Register Member" },
    { icon: Calendar, label: "Create Appointment" },
    // { icon: CreditCard, label: "Loan Payment" },
    // { icon: Users, label: "Group Collection" },
  ];

  return (
    <View style={[styles.container]}>
      {/* Header */}
      <View style={styles.header}>
        <View style={styles.headerContent}>
          <Text variant="title">Dashboard</Text>
          <Icon name={Bell} size={24} />
        </View>
      </View>

      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={{ paddingBottom: 0 }}
        showsVerticalScrollIndicator={false}
      >
        {/* User Greeting */}
        <View style={styles.greetingSection}>
          <Text variant="heading" style={styles.greetingTitle}>
            Hello, Agent Alex
          </Text>
          <Text variant="caption" style={styles.greetingSubtitle}>
            Field Agent · Nairobi
          </Text>
        </View>

        {/* Status Badges */}
        <View style={styles.statusRow}>
          <Badge
            variant="outline"
            style={{
              borderColor: !isOnline ? muted : "#10b981",
              borderWidth: 1,
              backgroundColor: !isOnline ? "#A0ADBB10" : "#10b98110",
            }}
          >
            <View
              style={{ flexDirection: "row", alignItems: "center", gap: 4 }}
            >
              <Icon
                name={Wifi}
                size={14}
                color={!isOnline ? muted : greenColor}
              />
              <Text
                style={{
                  fontSize: 12,
                  color: !isOnline ? muted : greenColor,
                  fontWeight: "600",
                }}
              >
                Online
              </Text>
            </View>
          </Badge>
          <Badge
            variant="outline"
            style={{
              borderColor: "#ff6b35",
              borderWidth: 1,
              backgroundColor: "#ff6b3510",
            }}
          >
            <View
              style={{ flexDirection: "row", alignItems: "center", gap: 4 }}
            >
              <Icon name={WifiOff} size={14} color={redColor} />
              <Text
                style={{ fontSize: 12, color: redColor, fontWeight: "600" }}
              >
                Offline
              </Text>
            </View>
          </Badge>
          <Badge variant="outline">
            <View
              style={{ flexDirection: "row", alignItems: "center", gap: 4 }}
            >
              <Text style={{ fontSize: 12 }}>🔔</Text>
              <Text style={{ fontSize: 12 }}>Sync Required</Text>
            </View>
          </Badge>
        </View>

        <Separator style={{ marginVertical: 16, paddingHorizontal: 20 }} />

        {/* Today's Activity */}
        <View style={styles.section}>
          <Text variant="title" style={styles.sectionTitle}>
            Today's Activity
          </Text>
          <View style={styles.activityGrid}>
            <Card style={styles.activityCard}>
              <Text variant="caption" style={styles.activityLabel}>
                Transactions
              </Text>
              <Text variant="heading" style={styles.activityValue}>
                12
              </Text>
            </Card>
            <Card style={styles.activityCard}>
              <Text variant="caption" style={styles.activityLabel}>
                Pending Sync
              </Text>
              <Text variant="heading" style={styles.activityValue}>
                3
              </Text>
            </Card>
          </View>
          <Card style={[styles.activityCard, { width: "100%" }]}>
            <Text variant="caption" style={styles.activityLabel}>
              Amount Collected
            </Text>
            <Text variant="title" style={styles.activityValue}>
              Ksh 15k
            </Text>
          </Card>
        </View>

        {/* Today's Appointments */}
        <View style={styles.section}>
          <Text variant="title" style={styles.sectionTitle}>
            Today's Appointments
          </Text>
          <Card style={styles.appointmentCard}>
            <View style={styles.appointmentContent}>
              <View style={styles.appointmentIcon}>
                <Icon name={Calendar} size={20} color={primaryColor} />
              </View>
              <View style={styles.appointmentDetails}>
                <Text variant="body" style={styles.appointmentTitle}>
                  Member Meeting
                </Text>
                <Text variant="caption" style={styles.appointmentTime}>
                  10:00 AM - 11:00 AM
                </Text>
              </View>
            </View>
          </Card>
        </View>

        {/* Portfolio Alerts */}
        <View style={styles.section}>
          <Text variant="title" style={styles.sectionTitle}>
            Portfolio Alerts
          </Text>
          <Card style={styles.alertCard}>
            <View style={styles.alertContent}>
              <View style={styles.alertIcon}>
                <Icon name={TriangleAlert} size={20} color="#ff6b35" />
              </View>
              <View style={styles.alertDetails}>
                <Text variant="body" style={styles.alertTitle}>
                  Overdue Loans
                </Text>
                <Text variant="caption" style={styles.alertSubtitle}>
                  15 members
                </Text>
              </View>
            </View>
          </Card>
        </View>

        {/* Quick Actions */}
        <View style={styles.section}>
          <Text variant="title" style={styles.sectionTitle}>
            Quick Actions
          </Text>
          <View style={styles.quickActionsWrapper}>
            {quickActions.map((action, idx) => (
              <TouchableOpacity
                key={idx}
                style={[styles.quickActionItem, { width: itemWidth }]}
              >
                <Card style={styles.quickActionCard}>
                  <Icon name={action.icon} size={26} color={mutedColor} />
                  <Text variant="caption" style={styles.quickActionText}>
                    {action.label}
                  </Text>
                </Card>
              </TouchableOpacity>
            ))}
          </View>
        </View>

        {/* Recent Activity */}
        <View style={styles.section}>
          <Text variant="title" style={styles.sectionTitle}>
            Recent Activity
          </Text>
          <View style={styles.recentActivityList}>
            <View style={styles.recentActivityItem}>
              <View style={styles.recentActivityIcon}>
                <Icon name={DollarSign} size={16} color={primaryColor} />
              </View>
              <View style={styles.recentActivityContent}>
                <Text variant="body">Transaction processed</Text>
                <Text variant="caption" style={styles.recentActivityTime}>
                  10:30 AM
                </Text>
              </View>
            </View>
            <View style={styles.recentActivityItem}>
              <View style={styles.recentActivityIcon}>
                <Icon name={User} size={16} color={primaryColor} />
              </View>
              <View style={styles.recentActivityContent}>
                <Text variant="body">Member profile updated</Text>
                <Text variant="caption" style={styles.recentActivityTime}>
                  9:15 AM
                </Text>
              </View>
            </View>
            <View style={styles.recentActivityItem}>
              <View style={styles.recentActivityIcon}>
                <Icon name={Calendar} size={16} color={primaryColor} />
              </View>
              <View style={styles.recentActivityContent}>
                <Text variant="body">Appointment scheduled</Text>
                <Text variant="caption" style={styles.recentActivityTime}>
                  8:00 AM
                </Text>
              </View>
            </View>
          </View>
        </View>

        {/* Sync Button */}
        <View style={styles.syncSection}>
          <Button style={styles.syncButton} icon={RefreshCw} size="lg">
            Sync Now
          </Button>
          <Text variant="caption" style={styles.syncText}>
            Last synced: 2 hours ago
          </Text>
        </View>
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  header: {
    paddingTop: 64,
    paddingHorizontal: 20,
    paddingBottom: 20,
  },
  headerContent: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
  },
  greetingSection: {
    paddingHorizontal: 20,
    paddingBottom: 20,
  },
  greetingTitle: {
    fontSize: 23,
    marginBottom: 4,
  },
  greetingSubtitle: {
    opacity: 0.7,
  },
  statusRow: {
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: 20,
    gap: 12,
    flexWrap: "wrap",
  },
  section: {
    paddingHorizontal: 20,
    marginBottom: 24,
  },
  sectionTitle: {
    marginBottom: 16,
    fontSize: 20,
  },
  activityGrid: {
    flexDirection: "row",
    gap: 12,
    marginBottom: 12,
  },
  activityCard: {
    flex: 1,
    padding: 16,
    alignItems: "flex-start",
  },
  activityLabel: {
    marginBottom: 8,
    opacity: 0.7,
  },
  activityValue: {
    fontSize: 22,
  },
  appointmentCard: {
    padding: 16,
  },
  appointmentContent: {
    flexDirection: "row",
    alignItems: "center",
  },
  appointmentIcon: {
    marginRight: 12,
    width: 40,
    height: 40,
    borderRadius: 5,
    backgroundColor: "#92C4F6",
    alignItems: "center",
    justifyContent: "center",
  },
  appointmentDetails: {
    flex: 1,
  },
  appointmentTitle: {
    fontWeight: "600",
    marginBottom: 4,
  },
  appointmentTime: {
    opacity: 0.7,
  },
  alertCard: {
    padding: 16,
  },
  alertContent: {
    flexDirection: "row",
    alignItems: "center",
  },
  alertIcon: {
    marginRight: 12,
    width: 40,
    height: 40,
    borderRadius: 8,
    backgroundColor: "#FFF2E5",
    alignItems: "center",
    justifyContent: "center",
  },
  alertDetails: {
    flex: 1,
  },
  alertTitle: {
    fontWeight: "600",
    marginBottom: 4,
  },
  alertSubtitle: {
    opacity: 0.7,
  },
  quickActionsWrapper: {
    flexDirection: "row",
    flexWrap: "wrap",
    justifyContent: "space-between",
    gap: 12,
  },
  quickActionItem: {
    marginBottom: 12,
  },
  quickActionCard: {
    padding: 16,
    alignItems: "center",
    justifyContent: "center",
    minHeight: 100,
    borderRadius: 12,
  },
  quickActionText: {
    textAlign: "center",
    marginTop: 8,
    fontSize: 12,
    opacity: 0.7,
  },
  recentActivityList: {
    gap: 16,
  },
  recentActivityItem: {
    flexDirection: "row",
    alignItems: "center",
  },
  recentActivityIcon: {
    marginRight: 12,
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: "#F2F2F7",
    alignItems: "center",
    justifyContent: "center",
  },
  recentActivityContent: {
    flex: 1,
  },
  recentActivityTime: {
    opacity: 0.7,
    marginTop: 2,
  },
  syncSection: {
    paddingHorizontal: 20,
    paddingBottom: 20,
    alignItems: "stretch",
  },
  syncButton: {
    width: "100%",
    marginBottom: 12,
  },
  syncText: {
    opacity: 0.7,
    textAlign: "center",
    fontSize: 15,
  },
});
